/* eslint-disable tailwindcss/classnames-order */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useCallback, useRef } from 'react';

import { FiSettings } from 'react-icons/fi';
import { PiPlusBold } from 'react-icons/pi';
import { GrHistory } from 'react-icons/gr';
import {
  type Message,
  Actors,
  chatHistoryStore,
  agentModelStore,
  generalSettingsStore,
  loginStore,
} from '@extension/storage';
import favoritesStorage, { type FavoritePrompt } from '@extension/storage/lib/prompt/favorites';
import MessageList from './components/MessageList';
import ChatInput from './components/ChatInput';
import ChatHistoryList from './components/ChatHistoryList';
import BookmarkList from './components/BookmarkList';
import TestCaseGenerator from './components/TestCaseGenerator';
import TestCaseResultsSummary from './components/TestCaseResultsSummary';
import { PlaywrightGenerator } from './components/PlaywrightGenerator';
import LoginComponent from './components/LoginComponent';
import { EventType, type AgentEvent, ExecutionState } from './types/event';
import type { TestCase } from './types/testcase';
import './SidePanel.css';

// Declare chrome API types
declare global {
  interface Window {
    chrome: typeof chrome;
  }
}

const SidePanel = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputEnabled, setInputEnabled] = useState(true);
  const [showStopButton, setShowStopButton] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [chatSessions, setChatSessions] = useState<Array<{ id: string; title: string; createdAt: number }>>([]);
  const [isFollowUpMode, setIsFollowUpMode] = useState(false);
  const [isHistoricalSession, setIsHistoricalSession] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [favoritePrompts, setFavoritePrompts] = useState<FavoritePrompt[]>([]);
  const [hasConfiguredModels, setHasConfiguredModels] = useState<boolean | null>(null); // null = loading, false = no models, true = has models
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);
  const [isReplaying, setIsReplaying] = useState(false);
  const [replayEnabled, setReplayEnabled] = useState(false);

  // Test case functionality state
  const [showTestCaseGenerator, setShowTestCaseGenerator] = useState(false);
  const [enableTestCases, setEnableTestCases] = useState(false);
  const [currentUserPrompt, setCurrentUserPrompt] = useState<string>('');
  const [generatedTestCases, setGeneratedTestCases] = useState<TestCase[]>([]);
  const [testCaseResults, setTestCaseResults] = useState<any[]>([]);

  // Playwright generator state
  const [showPlaywrightGenerator, setShowPlaywrightGenerator] = useState(false);

  // Login state
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState<any>(null);

  const sessionIdRef = useRef<string | null>(null);
  const isReplayingRef = useRef<boolean>(false);
  const portRef = useRef<chrome.runtime.Port | null>(null);
  const heartbeatIntervalRef = useRef<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const setInputTextRef = useRef<((text: string) => void) | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const recordingTimerRef = useRef<number | null>(null);

  // Check for dark mode preference
  useEffect(() => {
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setIsDarkMode(darkModeMediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsDarkMode(e.matches);
    };

    darkModeMediaQuery.addEventListener('change', handleChange);
    return () => darkModeMediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Check if models are configured
  const checkModelConfiguration = useCallback(async () => {
    try {
      const configuredAgents = await agentModelStore.getConfiguredAgents();

      // Check if at least one agent (preferably Navigator) is configured
      const hasAtLeastOneModel = configuredAgents.length > 0;
      setHasConfiguredModels(hasAtLeastOneModel);
    } catch (error) {
      console.error('Error checking model configuration:', error);
      setHasConfiguredModels(false);
    }
  }, []);

  // Load general settings to check if replay is enabled
  const loadGeneralSettings = useCallback(async () => {
    try {
      const settings = await generalSettingsStore.getSettings();
      setReplayEnabled(settings.replayHistoricalTasks);
    } catch (error) {
      console.error('Error loading general settings:', error);
      setReplayEnabled(false);
    }
  }, []);

  // Check login status
  const checkLoginStatus = useCallback(async () => {
    try {
      const loggedIn = await loginStore.isLoggedIn();
      setIsLoggedIn(loggedIn);

      if (loggedIn) {
        const user = await loginStore.getUserData();
        setUserData(user);
      }
    } catch (error) {
      console.error('Error checking login status:', error);
    }
  }, []);
  useEffect(() => {
    checkModelConfiguration();
    loadGeneralSettings();
    checkLoginStatus();
  }, [checkModelConfiguration, loadGeneralSettings, checkLoginStatus]);

  // Re-check model configuration when the side panel becomes visible again
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Panel became visible, re-check configuration and settings
        checkModelConfiguration();
        loadGeneralSettings();
      }
    };

    const handleFocus = () => {
      // Panel gained focus, re-check configuration and settings
      checkModelConfiguration();
      loadGeneralSettings();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [checkModelConfiguration, loadGeneralSettings]);

  useEffect(() => {
    sessionIdRef.current = currentSessionId;
  }, [currentSessionId]);

  useEffect(() => {
    isReplayingRef.current = isReplaying;
  }, [isReplaying]);

  const appendMessage = useCallback((newMessage: Message, sessionId?: string | null) => {
    // Don't save progress messages
    const isProgressMessage = newMessage.content === 'Showing progress...';

    setMessages(prev => {
      const filteredMessages = prev.filter(
        (msg, idx) => !(msg.content === 'Showing progress...' && idx === prev.length - 1),
      );
      return [...filteredMessages, newMessage];
    });

    // Use provided sessionId if available, otherwise fall back to sessionIdRef.current
    const effectiveSessionId = sessionId !== undefined ? sessionId : sessionIdRef.current;

    console.log('sessionId', effectiveSessionId);

    // Save message to storage if we have a session and it's not a progress message
    if (effectiveSessionId && !isProgressMessage) {
      chatHistoryStore
        .addMessage(effectiveSessionId, newMessage)
        .catch(err => console.error('Failed to save message to history:', err));
    }
  }, []);

  const handleTaskState = useCallback(
    (event: AgentEvent) => {
      const { actor, state, timestamp, data } = event;
      const content = data?.details;
      let skip = true;
      let displayProgress = false;

      console.log('handleTaskState isReplaying', isReplayingRef.current);
      switch (actor) {
        case Actors.SYSTEM:
          switch (state) {
            case ExecutionState.TASK_START:
              // Reset historical session flag when a new task starts
              setIsHistoricalSession(false);
              break;
            case ExecutionState.TASK_OK:
              setIsFollowUpMode(true);
              setInputEnabled(true);
              setShowStopButton(false);
              setIsReplaying(false);
              break;
            case ExecutionState.TASK_FAIL:
              setIsFollowUpMode(true);
              setInputEnabled(true);
              setShowStopButton(false);
              setIsReplaying(false);
              skip = false;
              break;
            case ExecutionState.TASK_CANCEL:
              setIsFollowUpMode(false);
              setInputEnabled(true);
              setShowStopButton(false);
              setIsReplaying(false);
              skip = false;
              break;
            case ExecutionState.TASK_PAUSE:
              break;
            case ExecutionState.TASK_RESUME:
              break;
            default:
              console.error('Invalid task state', state);
              return;
          }
          break;
        case Actors.USER:
          break;
        case Actors.PLANNER:
          switch (state) {
            case ExecutionState.STEP_START:
              displayProgress = true;
              break;
            case ExecutionState.STEP_OK:
              skip = false;
              break;
            case ExecutionState.STEP_FAIL:
              skip = false;
              break;
            case ExecutionState.STEP_CANCEL:
              break;
            default:
              console.error('Invalid step state', state);
              return;
          }
          break;
        case Actors.NAVIGATOR:
          switch (state) {
            case ExecutionState.STEP_START:
              displayProgress = true;
              break;
            case ExecutionState.STEP_OK:
              displayProgress = false;
              break;
            case ExecutionState.STEP_FAIL:
              skip = false;
              displayProgress = false;
              break;
            case ExecutionState.STEP_CANCEL:
              displayProgress = false;
              break;
            case ExecutionState.ACT_START:
              if (content !== 'cache_content') {
                // skip to display caching content
                skip = false;
              }
              break;
            case ExecutionState.ACT_OK:
              skip = !isReplayingRef.current;
              break;
            case ExecutionState.ACT_FAIL:
              skip = false;
              break;
            default:
              console.error('Invalid action', state);
              return;
          }
          break;
        case Actors.VALIDATOR:
          switch (state) {
            case ExecutionState.STEP_START:
              displayProgress = true;
              break;
            case ExecutionState.STEP_OK:
              skip = false;
              break;
            case ExecutionState.STEP_FAIL:
              skip = false;
              break;
            default:
              console.error('Invalid validation', state);
              return;
          }
          break;
        case Actors.TEST_CASE_GENERATOR:
          switch (state) {
            case ExecutionState.STEP_START:
              displayProgress = true;
              break;
            case ExecutionState.STEP_OK:
              skip = false;
              break;
            case ExecutionState.STEP_FAIL:
              skip = false;
              break;
            default:
              console.error('Invalid test case generation', state);
              return;
          }
          break;
        default:
          console.error('Unknown actor', actor);
          return;
      }

      if (!skip) {
        appendMessage({
          actor,
          content: content || '',
          timestamp: timestamp,
        });
      }

      if (displayProgress) {
        appendMessage({
          actor,
          content: 'Showing progress...',
          timestamp: timestamp,
        });
      }
    },
    [appendMessage],
  );

  // Stop heartbeat and close connection
  const stopConnection = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
    if (portRef.current) {
      portRef.current.disconnect();
      portRef.current = null;
    }
  }, []);

  // Setup connection management
  const setupConnection = useCallback(() => {
    // Only setup if no existing connection
    if (portRef.current) {
      return;
    }

    try {
      portRef.current = chrome.runtime.connect({ name: 'side-panel-connection' });

      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      portRef.current.onMessage.addListener((message: any) => {
        // Add type checking for message
        if (message && message.type === EventType.EXECUTION) {
          handleTaskState(message);
        } else if (message && message.type === 'error') {
          // Handle error messages from service worker
          appendMessage({
            actor: Actors.SYSTEM,
            content: message.error || 'Unknown error occurred',
            timestamp: Date.now(),
          });
          setInputEnabled(true);
          setShowStopButton(false);
        } else if (message && message.type === 'speech_to_text_result') {
          // Handle speech-to-text result
          if (message.text && setInputTextRef.current) {
            setInputTextRef.current(message.text);
          }
          setIsProcessingSpeech(false);
        } else if (message && message.type === 'speech_to_text_error') {
          // Handle speech-to-text error
          appendMessage({
            actor: Actors.SYSTEM,
            content: message.error || 'Speech recognition failed',
            timestamp: Date.now(),
          });
          setIsProcessingSpeech(false);
        } else if (message && message.type === 'heartbeat_ack') {
          console.log('Heartbeat acknowledged');
        }
      });

      portRef.current.onDisconnect.addListener(() => {
        const error = chrome.runtime.lastError;
        console.log('Connection disconnected', error ? `Error: ${error.message}` : '');
        portRef.current = null;
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }
        setInputEnabled(true);
        setShowStopButton(false);
      });

      // Setup heartbeat interval
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }

      heartbeatIntervalRef.current = window.setInterval(() => {
        if (portRef.current?.name === 'side-panel-connection') {
          try {
            portRef.current.postMessage({ type: 'heartbeat' });
          } catch (error) {
            console.error('Heartbeat failed:', error);
            stopConnection(); // Stop connection if heartbeat fails
          }
        } else {
          stopConnection(); // Stop if port is invalid
        }
      }, 25000);
    } catch (error) {
      console.error('Failed to establish connection:', error);
      appendMessage({
        actor: Actors.SYSTEM,
        content: 'Failed to connect to service worker',
        timestamp: Date.now(),
      });
      // Clear any references since connection failed
      portRef.current = null;
    }
  }, [handleTaskState, appendMessage, stopConnection]);

  // Add safety check for message sending
  const sendMessage = useCallback(
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    (message: any) => {
      if (portRef.current?.name !== 'side-panel-connection') {
        throw new Error('No valid connection available');
      }
      try {
        portRef.current.postMessage(message);
      } catch (error) {
        console.error('Failed to send message:', error);
        stopConnection(); // Stop connection when message sending fails
        throw error;
      }
    },
    [stopConnection],
  );

  // Handle test case generation
  const handleTestCasesGenerated = useCallback(
    (testCases: TestCase[]) => {
      setGeneratedTestCases(testCases);

      // Show a message that test cases have been generated
      appendMessage({
        actor: Actors.TEST_CASE_GENERATOR,
        content: `Generated ${testCases.length} test cases for your UI testing. Please select the test cases you want to execute.`,
        timestamp: Date.now(),
      });
    },
    [appendMessage],
  );

  // Handle test case toggle
  const handleTestCaseToggle = useCallback((enabled: boolean) => {
    setEnableTestCases(enabled);
    if (!enabled) {
      // If disabled, also hide the test case generator if it's currently shown
      setShowTestCaseGenerator(false);
    }
  }, []);

  // Handle test case execution completion
  const handleTestCaseExecutionComplete = useCallback(
    (results: any[]) => {
      setTestCaseResults(results);

      // Generate summary message
      const passedCount = results.filter(r => r.status === 'passed').length;
      const failedCount = results.filter(r => r.status === 'failed' || r.status === 'error').length;

      const summaryMessage = `Test execution completed! ${passedCount} tests passed, ${failedCount} tests failed.`;

      appendMessage({
        actor: Actors.TEST_CASE_GENERATOR,
        content: summaryMessage,
        timestamp: Date.now(),
      });

      // After showing results, continue with the normal planner/navigator flow
      setTimeout(() => {
        continueWithNormalFlow();
      }, 1000);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [appendMessage],
  );

  // Continue with normal planner/navigator flow after test case execution
  const continueWithNormalFlow = useCallback(async () => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const tabId = tabs[0]?.id;
      if (!tabId) {
        throw new Error('No active tab found');
      }

      // Setup connection if not exists
      if (!portRef.current) {
        setupConnection();
      }

      // Send message using the utility function
      if (isFollowUpMode) {
        // Send as follow-up task
        await sendMessage({
          type: 'follow_up_task',
          task: currentUserPrompt,
          taskId: sessionIdRef.current,
          tabId,
        });
        console.log('follow_up_task sent after test cases', currentUserPrompt, tabId, sessionIdRef.current);
      } else {
        // Send as new task
        await sendMessage({
          type: 'new_task',
          task: currentUserPrompt,
          taskId: sessionIdRef.current,
          tabId,
        });
        console.log('new_task sent after test cases', currentUserPrompt, tabId, sessionIdRef.current);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('Task error after test cases:', errorMessage);
      appendMessage({
        actor: Actors.SYSTEM,
        content: errorMessage,
        timestamp: Date.now(),
      });
      setInputEnabled(true);
      setShowStopButton(false);
      stopConnection();
    }
  }, [currentUserPrompt, isFollowUpMode, sendMessage, setupConnection, appendMessage, stopConnection]);

  // Handle individual test case execution through agent workflow
  const handleTestCaseExecution = useCallback(
    async (testCase: TestCase): Promise<string[]> => {
      const validatorResults: string[] = [];

      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        const tabId = tabs[0]?.id;
        if (!tabId) {
          throw new Error('No active tab found');
        }

        // Setup connection if not exists
        if (!portRef.current) {
          setupConnection();
        }

        // Add a message indicating test case execution started
        appendMessage({
          actor: Actors.TEST_CASE_GENERATOR,
          content: `🔄 Executing test case: ${testCase.name}`,
          timestamp: Date.now(),
        });

        // Create a test execution task based on the test case
        const testTask = `Execute the following test case: ${testCase.name}\n\nDescription: ${testCase.description}\n\nExpected Result: ${testCase.expected_result}\n\nBreaking Scenario: ${testCase.breaking_scenario}\n\nCategory: ${testCase.category}\nSeverity: ${testCase.severity}`;

        // Add test case details message
        appendMessage({
          actor: Actors.TEST_CASE_GENERATOR,
          content: `Test Case Details:\n- Name: ${testCase.name}\n- Category: ${testCase.category}\n- Severity: ${testCase.severity}\n- Description: ${testCase.description}`,
          timestamp: Date.now(),
        });

        // Create a promise that resolves when the test case completes
        const testCompletionPromise = new Promise<string>((resolve, reject) => {
          let isResolved = false;
          let validatorResult = '';

          const originalHandleTaskState = handleTaskState;

          // Temporarily override handleTaskState to detect completion and capture validator results
          const wrappedHandleTaskState = (event: any) => {
            // Call the original handler first
            originalHandleTaskState(event);

            // Capture validator messages
            if (event.actor === Actors.VALIDATOR && event.data?.details) {
              validatorResult = event.data.details;
            }

            // Check for task completion states
            if (!isResolved && event.actor === Actors.SYSTEM) {
              if (event.state === ExecutionState.TASK_OK) {
                isResolved = true;
                resolve(validatorResult || 'Test case completed successfully');
              } else if (event.state === ExecutionState.TASK_FAIL) {
                isResolved = true;
                reject(new Error(event.data?.details || 'Test case failed'));
              }
            }
          };

          // Set a timeout to prevent hanging
          const timeout = setTimeout(() => {
            if (!isResolved) {
              isResolved = true;
              reject(new Error('Test case execution timeout'));
            }
          }, 60000); // 60 second timeout for test cases

          // Override the message listener temporarily
          const originalListener = portRef.current?.onMessage;
          if (portRef.current) {
            portRef.current.onMessage.removeListener(originalListener as any);
            portRef.current.onMessage.addListener((message: any) => {
              if (message && message.type === EventType.EXECUTION) {
                wrappedHandleTaskState(message);
              }
              // Also handle other message types through original listener
              if (originalListener) {
                (originalListener as any)(message);
              }
            });
          }

          // Cleanup function
          const cleanup = () => {
            clearTimeout(timeout);
            if (portRef.current && originalListener) {
              portRef.current.onMessage.removeListener(wrappedHandleTaskState as any);
            }
          };

          // Ensure cleanup happens
          Promise.resolve().then(() => {
            if (isResolved) cleanup();
          });
        });

        // Send the test case as a task to the agent workflow
        await sendMessage({
          type: isFollowUpMode ? 'follow_up_task' : 'new_task',
          task: testTask,
          taskId: sessionIdRef.current,
          tabId,
        });

        // Wait for the test case to complete and capture validator result
        const validatorResult = await testCompletionPromise;
        validatorResults.push(validatorResult);

        // Add completion message
        appendMessage({
          actor: Actors.TEST_CASE_GENERATOR,
          content: `✅ Test case "${testCase.name}" execution completed`,
          timestamp: Date.now(),
        });

        return validatorResults;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error('Test case execution error:', errorMessage);

        appendMessage({
          actor: Actors.TEST_CASE_GENERATOR,
          content: `❌ Test case "${testCase.name}" failed: ${errorMessage}`,
          timestamp: Date.now(),
        });

        throw err; // Re-throw to be handled by the test case generator
      }
    },
    [isFollowUpMode, sendMessage, setupConnection, appendMessage, handleTaskState],
  );

  // Handle replay command
  const handleReplay = async (historySessionId: string): Promise<void> => {
    try {
      // Check if replay is enabled in settings
      if (!replayEnabled) {
        appendMessage({
          actor: Actors.SYSTEM,
          content:
            'Replay is disabled in general settings. Please enable "Replay Historical Tasks" in the extension settings to use this feature.',
          timestamp: Date.now(),
        });
        return;
      }

      // Check if history exists using loadAgentStepHistory
      const historyData = await chatHistoryStore.loadAgentStepHistory(historySessionId);
      if (!historyData) {
        appendMessage({
          actor: Actors.SYSTEM,
          content: `No action history found for session "${historySessionId.substring(0, 20)}...". This session may not contain replayable actions. \n\nIt's a replay session itself (replay sessions cannot be replayed again), or it was created before the replay feature was available.`,
          timestamp: Date.now(),
        });
        return;
      }

      // Get current tab ID
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const tabId = tabs[0]?.id;
      if (!tabId) {
        throw new Error('No active tab found');
      }

      // Clear messages if we're in a historical session
      if (isHistoricalSession) {
        setMessages([]);
      }

      // Create a new chat session for this replay task
      const newSession = await chatHistoryStore.createSession(`Replay of ${historySessionId.substring(0, 20)}...`);
      console.log('newSession for replay', newSession);

      // Store the new session ID in both state and ref
      const newTaskId = newSession.id;
      setCurrentSessionId(newTaskId);
      sessionIdRef.current = newTaskId;

      // Send replay command to background
      setInputEnabled(false);
      setShowStopButton(true);

      // Reset follow-up mode and historical session flags
      setIsFollowUpMode(false);
      setIsHistoricalSession(false);

      const userMessage = {
        actor: Actors.USER,
        content: `/replay ${historySessionId}`,
        timestamp: Date.now(),
      };

      // Add the user message to the new session
      appendMessage(userMessage, sessionIdRef.current);

      // Setup connection if not exists
      if (!portRef.current) {
        setupConnection();
      }

      // Send replay command to background with the task from history
      portRef.current?.postMessage({
        type: 'replay',
        taskId: newTaskId,
        tabId: tabId,
        historySessionId: historySessionId,
        task: historyData.task, // Add the task from history
      });

      appendMessage({
        actor: Actors.SYSTEM,
        content: `Starting replay of task:\n\n"${historyData.task}"`,
        timestamp: Date.now(),
      });
      setIsReplaying(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      appendMessage({
        actor: Actors.SYSTEM,
        content: `Replay failed: ${errorMessage}`,
        timestamp: Date.now(),
      });
    }
  };

  // Handle chat commands that start with /
  const handleCommand = async (command: string): Promise<boolean> => {
    try {
      // Setup connection if not exists
      if (!portRef.current) {
        setupConnection();
      }

      // Handle different commands
      if (command === '/state') {
        portRef.current?.postMessage({
          type: 'state',
        });
        return true;
      }

      if (command === '/nohighlight') {
        portRef.current?.postMessage({
          type: 'nohighlight',
        });
        return true;
      }

      if (command.startsWith('/replay ')) {
        // Parse replay command: /replay <historySessionId>
        // Handle multiple spaces by filtering out empty strings
        const parts = command.split(' ').filter(part => part.trim() !== '');
        if (parts.length !== 2) {
          appendMessage({
            actor: Actors.SYSTEM,
            content: 'Invalid replay command format. Usage: /replay <historySessionId>',
            timestamp: Date.now(),
          });
          return true;
        }

        const historySessionId = parts[1];
        await handleReplay(historySessionId);
        return true;
      }

      // Unsupported command
      appendMessage({
        actor: Actors.SYSTEM,
        content: `Unsupported command: ${command}. \n\nAvailable commands: /state, /nohighlight, /replay <historySessionId>`,
        timestamp: Date.now(),
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('Command error', errorMessage);
      appendMessage({
        actor: Actors.SYSTEM,
        content: errorMessage,
        timestamp: Date.now(),
      });
      return true;
    }
  };

  const handleSendMessage = async (text: string) => {
    console.log('handleSendMessage', text);

    // Trim the input text first
    const trimmedText = text.trim();

    if (!trimmedText) return;

    // Check if the input is a command (starts with /)
    if (trimmedText.startsWith('/')) {
      // Process command and return if it was handled
      const wasHandled = await handleCommand(trimmedText);
      if (wasHandled) return;
    }

    // Block sending messages in historical sessions
    if (isHistoricalSession) {
      console.log('Cannot send messages in historical sessions');
      return;
    }

    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const tabId = tabs[0]?.id;
      if (!tabId) {
        throw new Error('No active tab found');
      }

      setInputEnabled(false);
      setShowStopButton(true);

      // Create a new chat session for this task if not in follow-up mode
      if (!isFollowUpMode) {
        const newSession = await chatHistoryStore.createSession(
          text.substring(0, 50) + (text.length > 50 ? '...' : ''),
        );
        console.log('newSession', newSession);

        // Store the session ID in both state and ref
        const sessionId = newSession.id;
        setCurrentSessionId(sessionId);
        sessionIdRef.current = sessionId;
      }

      const userMessage = {
        actor: Actors.USER,
        content: text,
        timestamp: Date.now(),
      };

      // Pass the sessionId directly to appendMessage
      appendMessage(userMessage, sessionIdRef.current);

      // Store the current user prompt for later use
      setCurrentUserPrompt(trimmedText);

      // Show test case generator first if not in follow-up mode and test cases are enabled
      if (!isFollowUpMode && enableTestCases) {
        appendMessage({
          actor: Actors.TEST_CASE_GENERATOR,
          content: 'Generating test cases for your UI...',
          timestamp: Date.now(),
        });

        setShowTestCaseGenerator(true);
        // The test case generator component will handle the rest
        return;
      }

      // For follow-up mode or when test cases are disabled, continue with normal flow
      // Setup connection if not exists
      if (!portRef.current) {
        setupConnection();
      }

      // Send message using the utility function
      if (isFollowUpMode) {
        // Send as follow-up task
        await sendMessage({
          type: 'follow_up_task',
          task: text,
          taskId: sessionIdRef.current,
          tabId,
        });
        console.log('follow_up_task sent', text, tabId, sessionIdRef.current);
      } else {
        // Send as new task
        await sendMessage({
          type: 'new_task',
          task: text,
          taskId: sessionIdRef.current,
          tabId,
        });
        console.log('new_task sent', text, tabId, sessionIdRef.current);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('Task error', errorMessage);
      appendMessage({
        actor: Actors.SYSTEM,
        content: errorMessage,
        timestamp: Date.now(),
      });
      setInputEnabled(true);
      setShowStopButton(false);
      stopConnection();
    }
  };

  const handleStopTask = async () => {
    try {
      portRef.current?.postMessage({
        type: 'cancel_task',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('cancel_task error', errorMessage);
      appendMessage({
        actor: Actors.SYSTEM,
        content: errorMessage,
        timestamp: Date.now(),
      });
    }
    setInputEnabled(true);
    setShowStopButton(false);
  };

  const handleNewChat = () => {
    setMessages([]);
    setInputEnabled(true);
    setShowStopButton(false);
    setCurrentSessionId(null);
    setIsFollowUpMode(false);
    setIsHistoricalSession(false);
    setShowTestCaseGenerator(false);
    setShowPlaywrightGenerator(false);
    setGeneratedTestCases([]);
    setTestCaseResults([]);
    setCurrentUserPrompt('');
    setIsReplaying(false);
    isReplayingRef.current = false;
    sessionIdRef.current = null;
    setInputTextRef.current?.('');
  };

  const loadChatSessions = useCallback(async () => {
    try {
      const sessions = await chatHistoryStore.getSessionsMetadata();
      setChatSessions(sessions.sort((a, b) => b.createdAt - a.createdAt));
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
    }
  }, []);

  const handleLoadHistory = async () => {
    await loadChatSessions();
    setShowHistory(true);
  };

  const handleBackToChat = (reset = false) => {
    setShowHistory(false);
    if (reset) {
      setCurrentSessionId(null);
      setMessages([]);
      setIsFollowUpMode(false);
      setIsHistoricalSession(false);
    }
  };

  const handleSessionSelect = async (sessionId: string) => {
    try {
      const fullSession = await chatHistoryStore.getSession(sessionId);
      if (fullSession && fullSession.messages.length > 0) {
        setCurrentSessionId(fullSession.id);
        setMessages(fullSession.messages);
        setIsFollowUpMode(false);
        setIsHistoricalSession(true); // Mark this as a historical session
        console.log('history session selected', sessionId);
      }
      setShowHistory(false);
    } catch (error) {
      console.error('Failed to load session:', error);
    }
  };

  const handleSessionDelete = async (sessionId: string) => {
    try {
      await chatHistoryStore.deleteSession(sessionId);
      await loadChatSessions();
      if (sessionId === currentSessionId) {
        setMessages([]);
        setCurrentSessionId(null);
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  };

  const handleSessionBookmark = async (sessionId: string) => {
    try {
      const fullSession = await chatHistoryStore.getSession(sessionId);

      if (fullSession && fullSession.messages.length > 0) {
        // Get the session title
        const sessionTitle = fullSession.title;
        // Get the first 8 words of the title
        const title = sessionTitle.split(' ').slice(0, 8).join(' ');

        // Get the first message content (the task)
        const taskContent = fullSession.messages[0]?.content || '';

        // Add to favorites storage
        await favoritesStorage.addPrompt(title, taskContent);

        // Update favorites in the UI
        const prompts = await favoritesStorage.getAllPrompts();
        setFavoritePrompts(prompts);

        // Return to chat view after pinning
        handleBackToChat(true);
      }
    } catch (error) {
      console.error('Failed to pin session to favorites:', error);
    }
  };

  const handleBookmarkSelect = (content: string) => {
    if (setInputTextRef.current) {
      setInputTextRef.current(content);
    }
  };

  const handleBookmarkUpdateTitle = async (id: number, title: string) => {
    try {
      await favoritesStorage.updatePromptTitle(id, title);

      // Update favorites in the UI
      const prompts = await favoritesStorage.getAllPrompts();
      setFavoritePrompts(prompts);
    } catch (error) {
      console.error('Failed to update favorite prompt title:', error);
    }
  };

  const handleBookmarkDelete = async (id: number) => {
    try {
      await favoritesStorage.removePrompt(id);

      // Update favorites in the UI
      const prompts = await favoritesStorage.getAllPrompts();
      setFavoritePrompts(prompts);
    } catch (error) {
      console.error('Failed to delete favorite prompt:', error);
    }
  };

  const handleBookmarkReorder = async (draggedId: number, targetId: number) => {
    try {
      // Directly pass IDs to storage function - it now handles the reordering logic
      await favoritesStorage.reorderPrompts(draggedId, targetId);

      // Fetch the updated list from storage to get the new IDs and reflect the authoritative order
      const updatedPromptsFromStorage = await favoritesStorage.getAllPrompts();
      setFavoritePrompts(updatedPromptsFromStorage);
    } catch (error) {
      console.error('Failed to reorder favorite prompts:', error);
    }
  };

  // Load favorite prompts from storage
  useEffect(() => {
    const loadFavorites = async () => {
      try {
        const prompts = await favoritesStorage.getAllPrompts();
        setFavoritePrompts(prompts);
      } catch (error) {
        console.error('Failed to load favorite prompts:', error);
      }
    };

    loadFavorites();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Stop recording if active
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
      // Clear recording timer
      if (recordingTimerRef.current) {
        clearTimeout(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
      stopConnection();
    };
  }, [stopConnection]);

  // Scroll to bottom when new messages arrive
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleMicClick = async () => {
    if (isRecording) {
      // Stop recording
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
      // Clear the timer
      if (recordingTimerRef.current) {
        clearTimeout(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
      setIsRecording(false);
      return;
    }

    try {
      // First check if permission is already granted
      const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });

      if (permissionStatus.state === 'denied') {
        appendMessage({
          actor: Actors.SYSTEM,
          content: 'Microphone access denied. Please enable microphone permissions in Chrome settings.',
          timestamp: Date.now(),
        });
        return;
      }

      // If permission is not granted, open permission page
      if (permissionStatus.state !== 'granted') {
        const permissionUrl = chrome.runtime.getURL('permission/index.html');

        // Open permission page in a new window
        chrome.windows.create(
          {
            url: permissionUrl,
            type: 'popup',
            width: 500,
            height: 600,
          },
          createdWindow => {
            if (createdWindow?.id) {
              // Listen for window close to check permission status
              chrome.windows.onRemoved.addListener(function onWindowClose(windowId) {
                if (windowId === createdWindow.id) {
                  chrome.windows.onRemoved.removeListener(onWindowClose);
                  // Check permission status after window closes
                  setTimeout(async () => {
                    try {
                      const newPermissionStatus = await navigator.permissions.query({
                        name: 'microphone' as PermissionName,
                      });
                      // Only retry if permission was granted
                      if (newPermissionStatus.state === 'granted') {
                        handleMicClick();
                      }
                      // If denied or prompt, do nothing - let user manually try again
                    } catch (error) {
                      console.error('Failed to check permission status:', error);
                    }
                  }, 500);
                }
              });
            }
          },
        );
        return;
      }

      // Permission granted - proceed with recording
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Clear previous audio chunks
      audioChunksRef.current = [];

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Handle data available event
      mediaRecorder.ondataavailable = event => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle stop event
      mediaRecorder.onstop = async () => {
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());

        if (audioChunksRef.current.length > 0) {
          // Create audio blob
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

          // Convert blob to base64
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64Audio = reader.result as string;

            // Setup connection if not exists
            if (!portRef.current) {
              setupConnection();
            }

            // Send audio to backend for speech-to-text conversion
            try {
              setIsProcessingSpeech(true);
              portRef.current?.postMessage({
                type: 'speech_to_text',
                audio: base64Audio,
              });
            } catch (error) {
              console.error('Failed to send audio for speech-to-text:', error);
              appendMessage({
                actor: Actors.SYSTEM,
                content: 'Failed to process speech recording',
                timestamp: Date.now(),
              });
              setIsRecording(false);
              setIsProcessingSpeech(false);
            }
          };
          reader.readAsDataURL(audioBlob);
        }
      };

      // Set up 2-minute duration limit
      const maxDuration = 2 * 60 * 1000;
      recordingTimerRef.current = window.setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop();
        }
        setIsRecording(false);
        setIsProcessingSpeech(true);
        recordingTimerRef.current = null;
      }, maxDuration);

      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);

      let errorMessage = 'Failed to access microphone. ';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage += 'Please grant microphone permission.';
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'No microphone found.';
        } else {
          errorMessage += error.message;
        }
      }

      appendMessage({
        actor: Actors.SYSTEM,
        content: errorMessage,
        timestamp: Date.now(),
      });
      setIsRecording(false);
    }
  };

  if (!isLoggedIn) {
    return (
      <div
        className={`flex h-screen flex-col ${isDarkMode ? 'bg-[#0d0d22]' : "bg-[url('/bg.jpg')] bg-cover bg-no-repeat"} overflow-hidden border ${isDarkMode ? 'border-[#2e2e60]' : 'border-[rgb(186,230,253)]'} rounded-2xl`}>
        <header className="header relative bg-white/80 backdrop-blur-sm border-b border-purple-200">
          <div className="header-logo">
            <img src="/icon-128.png" alt="Extension Logo" className="size-6" />
          </div>
        </header>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-sm">
            <LoginComponent
              onLoginSuccess={() => {
                checkLoginStatus();
              }}
              onLogout={() => {
                setIsLoggedIn(false);
                setUserData(null);
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div
        className={`flex h-screen flex-col ${isDarkMode ? 'bg-[#0d0d22]' : "bg-[url('/bg.jpg')] bg-cover bg-no-repeat"} overflow-hidden border ${isDarkMode ? 'border-[#2e2e60]' : 'border-[rgb(186,230,253)]'} rounded-2xl`}>
        <header className="header relative">
          <div className="header-logo">
            {showHistory ? (
              <button
                type="button"
                onClick={() => handleBackToChat(false)}
                className={`${isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-400 hover:text-sky-500'} cursor-pointer`}
                aria-label="Back to chat">
                ← Back
              </button>
            ) : (
              <img src="/icon-128.png" alt="Extension Logo" className="size-6" />
            )}
          </div>
          <div className="header-icons">
            {!showHistory && (
              <>
                <button
                  type="button"
                  onClick={handleNewChat}
                  onKeyDown={e => e.key === 'Enter' && handleNewChat()}
                  className={`header-icon ${isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-400 hover:text-sky-500'} cursor-pointer`}
                  aria-label="New Chat"
                  tabIndex={0}>
                  <PiPlusBold size={20} />
                </button>
                <button
                  type="button"
                  onClick={handleLoadHistory}
                  onKeyDown={e => e.key === 'Enter' && handleLoadHistory()}
                  className={`header-icon ${isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-400 hover:text-sky-500'} cursor-pointer`}
                  aria-label="Load History"
                  tabIndex={0}>
                  <GrHistory size={20} />
                </button>
                <button
                  type="button"
                  onClick={() => setShowPlaywrightGenerator(!showPlaywrightGenerator)}
                  onKeyDown={e => e.key === 'Enter' && setShowPlaywrightGenerator(!showPlaywrightGenerator)}
                  className={`header-icon ${isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-400 hover:text-sky-500'} cursor-pointer`}
                  aria-label="Playwright Generator"
                  tabIndex={0}>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14,2 14,8 20,8" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <polyline points="10,9 9,9 8,9" />
                  </svg>
                </button>
              </>
            )}
            <button
              type="button"
              onClick={() => chrome.runtime.openOptionsPage()}
              onKeyDown={e => e.key === 'Enter' && chrome.runtime.openOptionsPage()}
              className={`header-icon ${isDarkMode ? 'text-[#875bf8] hover:text-[#a478f9]' : 'text-sky-400 hover:text-sky-500'} cursor-pointer`}
              aria-label="Settings"
              tabIndex={0}>
              <FiSettings size={20} />
            </button>
          </div>
        </header>
        {showHistory ? (
          <div className="flex-1 overflow-hidden">
            <ChatHistoryList
              sessions={chatSessions}
              onSessionSelect={handleSessionSelect}
              onSessionDelete={handleSessionDelete}
              onSessionBookmark={handleSessionBookmark}
              visible={true}
              isDarkMode={isDarkMode}
            />
          </div>
        ) : (
          <>
            {/* Show loading state while checking model configuration */}
            {hasConfiguredModels === null && (
              <div
                className={`flex flex-1 items-center justify-center p-8 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                <div className="text-center">
                  <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-2 border-[#875bf8] border-t-transparent"></div>
                  <p>Checking configuration...</p>
                </div>
              </div>
            )}

            {/* Show setup message when no models are configured */}
            {hasConfiguredModels === false && (
              <div
                className={`flex flex-1 items-center justify-center p-8 ${isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'}`}>
                <div className="max-w-md text-center">
                  <img src="/icon-128.png" alt="DrCode: UI Testing Logo" className="mx-auto mb-4 size-12" />
                  <h3 className={`mb-2 text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-sky-700'}`}>
                    Welcome to DrCode: UI Testing!
                  </h3>
                  <p className={`mb-4 ${isDarkMode ? 'text-white' : 'text-gray-600'}`}>
                    To get started, please configure your API keys in the settings page.
                  </p>
                  <button
                    onClick={() => chrome.runtime.openOptionsPage()}
                    className={`my-4 rounded-lg px-4 py-2 font-medium transition-colors ${
                      isDarkMode
                        ? 'bg-[#875bf8] text-white hover:bg-[#a478f9]'
                        : 'bg-sky-500 text-white hover:bg-sky-600'
                    }`}>
                    Open Settings
                  </button>
                </div>
              </div>
            )}

            {/* Show normal chat interface when models are configured */}
            {hasConfiguredModels === true && (
              <>
                {/* Playwright Generator */}
                {showPlaywrightGenerator && (
                  <div className="flex-1 overflow-hidden">
                    <PlaywrightGenerator isDarkMode={isDarkMode} />
                  </div>
                )}

                {/* Normal chat interface when Playwright generator is not shown */}
                {!showPlaywrightGenerator && (
                  <>
                    {messages.length === 0 && (
                      <>
                        <div
                          className={`border-t ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-100'} mb-2 p-2 shadow-sm backdrop-blur-sm`}>
                          <ChatInput
                            onSendMessage={handleSendMessage}
                            onStopTask={handleStopTask}
                            onMicClick={handleMicClick}
                            isRecording={isRecording}
                            isProcessingSpeech={isProcessingSpeech}
                            disabled={!inputEnabled || isHistoricalSession}
                            showStopButton={showStopButton}
                            setContent={setter => {
                              setInputTextRef.current = setter;
                            }}
                            isDarkMode={isDarkMode}
                            historicalSessionId={isHistoricalSession && replayEnabled ? currentSessionId : null}
                            onReplay={handleReplay}
                            enableTestCases={enableTestCases}
                            onTestCaseToggle={handleTestCaseToggle}
                          />
                        </div>
                        <div className="flex-1 overflow-y-auto">
                          <BookmarkList
                            bookmarks={favoritePrompts}
                            onBookmarkSelect={handleBookmarkSelect}
                            onBookmarkUpdateTitle={handleBookmarkUpdateTitle}
                            onBookmarkDelete={handleBookmarkDelete}
                            onBookmarkReorder={handleBookmarkReorder}
                            isDarkMode={isDarkMode}
                          />
                        </div>
                      </>
                    )}
                    {messages.length > 0 && (
                      <>
                        <div
                          className={`scrollbar-gutter-stable flex-1 overflow-x-hidden overflow-y-scroll scroll-smooth p-2 scrollbar-thin ${
                            isDarkMode ? 'bg-[#0d0d22]/80 scrollbar-track-dark' : 'scrollbar-track-light'
                          }`}>
                          <MessageList messages={messages} isDarkMode={isDarkMode} />

                          {/* Test Case Generator */}
                          {showTestCaseGenerator && (
                            <TestCaseGenerator
                              userPrompt={currentUserPrompt}
                              onTestCasesGenerated={handleTestCasesGenerated}
                              onExecutionComplete={handleTestCaseExecutionComplete}
                              onTestCaseExecution={handleTestCaseExecution}
                              isDarkMode={isDarkMode}
                            />
                          )}

                          {/* Test Case Results Summary */}
                          {testCaseResults && testCaseResults.length > 0 && (
                            <TestCaseResultsSummary results={testCaseResults} isDarkMode={isDarkMode} />
                          )}

                          <div ref={messagesEndRef} />
                        </div>
                      </>
                    )}
                    {messages.length > 0 && (
                      <div
                        className={`border-t ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-100'} p-2 shadow-sm backdrop-blur-sm`}>
                        <ChatInput
                          onSendMessage={handleSendMessage}
                          onStopTask={handleStopTask}
                          onMicClick={handleMicClick}
                          isRecording={isRecording}
                          isProcessingSpeech={isProcessingSpeech}
                          disabled={!inputEnabled || isHistoricalSession}
                          showStopButton={showStopButton}
                          setContent={setter => {
                            setInputTextRef.current = setter;
                          }}
                          isDarkMode={isDarkMode}
                          historicalSessionId={isHistoricalSession && replayEnabled ? currentSessionId : null}
                          onReplay={handleReplay}
                          enableTestCases={enableTestCases}
                          onTestCaseToggle={handleTestCaseToggle}
                        />
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SidePanel;
