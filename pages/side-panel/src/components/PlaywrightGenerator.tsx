import React, { useState, useEffect } from 'react';
import {
  FiPlay,
  FiDownload,
  FiRefreshCw,
  FiTrash2,
  FiCopy,
  FiCode,
  FiSettings,
  FiLoader,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

interface PlaywrightScriptOptions {
  language: 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
  includeComments: boolean;
  includeAssertions: boolean;
  includeWaitFor: boolean;
}

interface SessionInfo {
  sessionId: string | null;
  actionCount: number;
}

interface LanguageOption {
  value: string;
  label: string;
}

interface PlaywrightGeneratorProps {
  isDarkMode?: boolean;
}

export const PlaywrightGenerator: React.FC<PlaywrightGeneratorProps> = ({ isDarkMode = false }) => {
  const [scriptOptions, setScriptOptions] = useState<PlaywrightScriptOptions>({
    language: 'javascript',
    includeComments: true,
    includeAssertions: true,
    includeWaitFor: true,
  });

  const [sessionInfo, setSessionInfo] = useState<SessionInfo>({
    sessionId: null,
    actionCount: 0,
  });

  const [availableLanguages, setAvailableLanguages] = useState<LanguageOption[]>([
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'csharp', label: 'C#' },
  ]);
  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadActionTrackerInfo();
  }, []);

  const loadActionTrackerInfo = async () => {
    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({ type: 'get_action_tracker_info' });

      port.onMessage.addListener(message => {
        if (message.type === 'action_tracker_info') {
          setSessionInfo(message.sessionInfo);
          setAvailableLanguages(message.availableLanguages);
        } else if (message.type === 'error') {
          setError(message.error);
        }
      });
    } catch (error) {
      setError('Failed to load action tracker info');
    }
  };

  const generateScript = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 'generate_playwright_script',
        options: scriptOptions,
      });

      port.onMessage.addListener(message => {
        if (message.type === 'playwright_script_generated') {
          setGeneratedScript(message.script);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to generate Playwright script');
      setIsLoading(false);
    }
  };

  const exportScript = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 'export_playwright_script',
        options: {
          ...scriptOptions,
          filename: `playwright-test-${Date.now()}.${getFileExtension(scriptOptions.language)}`,
        },
      });

      port.onMessage.addListener(message => {
        if (message.type === 'playwright_script_exported') {
          // Script was exported successfully
          setError(null);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to export Playwright script');
      setIsLoading(false);
    }
  };

  const resetTracker = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({ type: 'reset_action_tracker' });

      port.onMessage.addListener(message => {
        if (message.type === 'action_tracker_reset') {
          loadActionTrackerInfo(); // Reload info after reset
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to reset action tracker');
      setIsLoading(false);
    }
  };

  const reRunActions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const port = chrome.runtime.connect({ name: 'side-panel-connection' });

      port.postMessage({
        type: 're_run_actions',
      });

      port.onMessage.addListener(message => {
        if (message.type === 'actions_re_run_complete') {
          setError(null);
        } else if (message.type === 'error') {
          setError(message.error);
        }
        setIsLoading(false);
      });
    } catch (error) {
      setError('Failed to re-run actions');
      setIsLoading(false);
    }
  };

  const getFileExtension = (language: string): string => {
    switch (language) {
      case 'typescript':
        return 'ts';
      case 'python':
        return 'py';
      case 'java':
        return 'java';
      case 'csharp':
        return 'cs';
      default:
        return 'js';
    }
  };

  return (
    <div className={`space-y-6 p-6 rounded-xl border transition-all duration-200 ${
      isDarkMode
        ? 'bg-[#1b1b41]/80 border-[#2e2e60] backdrop-blur-sm'
        : 'bg-white/90 border-sky-200 backdrop-blur-sm shadow-lg'
    }`}>
      {/* Header */}
      <div className={`border-b pb-4 ${isDarkMode ? 'border-[#2e2e60]' : 'border-sky-200'}`}>
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${
            isDarkMode ? 'bg-[#875bf8]/20' : 'bg-sky-100'
          }`}>
            <FiCode className={`w-6 h-6 ${
              isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'
            }`} />
          </div>
          <div>
            <h2 className={`text-xl font-bold ${
              isDarkMode ? 'text-gray-200' : 'text-gray-800'
            }`}>
              Playwright Script Generator
            </h2>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Generate Playwright test scripts from tracked browser actions
            </p>
          </div>
        </div>
      </div>

      {/* Session Info */}
      <div className={`rounded-lg p-4 border ${
        isDarkMode
          ? 'bg-[#2a2a5a]/50 border-[#875bf8]/30'
          : 'bg-sky-50 border-sky-200'
      }`}>
        <div className="flex items-center space-x-2 mb-3">
          <FiInfo className={`w-4 h-4 ${
            isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'
          }`} />
          <h3 className={`text-sm font-semibold ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            Session Information
          </h3>
        </div>
        <div className={`space-y-2 text-sm ${
          isDarkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          <div className="flex justify-between">
            <span>Session ID:</span>
            <span className="font-mono text-xs">
              {sessionInfo.sessionId || 'None'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Tracked Actions:</span>
            <span className={`font-semibold ${
              sessionInfo.actionCount > 0
                ? isDarkMode ? 'text-green-400' : 'text-green-600'
                : isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {sessionInfo.actionCount}
            </span>
          </div>
        </div>
      </div>

      {/* Script Options */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <FiSettings className={`w-4 h-4 ${
            isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'
          }`} />
          <h3 className={`text-lg font-semibold ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            Script Options
          </h3>
        </div>

        {/* Language Selection */}
        <div className="space-y-2">
          <label htmlFor="language-select" className={`block text-sm font-medium ${
            isDarkMode ? 'text-gray-300' : 'text-gray-700'
          }`}>
            Programming Language
          </label>
          <select
            id="language-select"
            value={scriptOptions.language}
            onChange={e =>
              setScriptOptions({
                ...scriptOptions,
                language: e.target.value as PlaywrightScriptOptions['language'],
              })
            }
            className={`w-full rounded-lg border px-4 py-3 transition-colors focus:outline-none focus:ring-2 ${
              isDarkMode
                ? 'bg-[#2a2a5a] border-[#2e2e60] text-gray-200 focus:ring-[#875bf8] focus:border-[#875bf8]'
                : 'bg-white border-sky-200 text-gray-800 focus:ring-sky-500 focus:border-sky-500'
            }`}>
            {availableLanguages.map(lang => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
        </div>

        {/* Checkbox Options */}
        <div className="space-y-3">
          <label className="flex items-center space-x-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={scriptOptions.includeComments}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeComments: e.target.checked,
                })
              }
              className={`w-4 h-4 rounded border-2 transition-colors ${
                isDarkMode
                  ? 'border-[#2e2e60] bg-[#2a2a5a] checked:bg-[#875bf8] checked:border-[#875bf8] focus:ring-[#875bf8]'
                  : 'border-sky-300 bg-white checked:bg-sky-500 checked:border-sky-500 focus:ring-sky-500'
              }`}
            />
            <span className={`text-sm font-medium group-hover:${
              isDarkMode ? 'text-gray-200' : 'text-gray-800'
            } ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Include comments in generated code
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={scriptOptions.includeAssertions}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeAssertions: e.target.checked,
                })
              }
              className={`w-4 h-4 rounded border-2 transition-colors ${
                isDarkMode
                  ? 'border-[#2e2e60] bg-[#2a2a5a] checked:bg-[#875bf8] checked:border-[#875bf8] focus:ring-[#875bf8]'
                  : 'border-sky-300 bg-white checked:bg-sky-500 checked:border-sky-500 focus:ring-sky-500'
              }`}
            />
            <span className={`text-sm font-medium group-hover:${
              isDarkMode ? 'text-gray-200' : 'text-gray-800'
            } ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Include assertions for validation
            </span>
          </label>

          <label className="flex items-center space-x-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={scriptOptions.includeWaitFor}
              onChange={e =>
                setScriptOptions({
                  ...scriptOptions,
                  includeWaitFor: e.target.checked,
                })
              }
              className={`w-4 h-4 rounded border-2 transition-colors ${
                isDarkMode
                  ? 'border-[#2e2e60] bg-[#2a2a5a] checked:bg-[#875bf8] checked:border-[#875bf8] focus:ring-[#875bf8]'
                  : 'border-sky-300 bg-white checked:bg-sky-500 checked:border-sky-500 focus:ring-sky-500'
              }`}
            />
            <span className={`text-sm font-medium group-hover:${
              isDarkMode ? 'text-gray-200' : 'text-gray-800'
            } ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Include wait between actions and page load waits
            </span>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={generateScript}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${
            isLoading || sessionInfo.actionCount === 0
              ? isDarkMode
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : isDarkMode
                ? 'bg-[#875bf8] text-white hover:bg-[#a478f9] shadow-lg shadow-[#875bf8]/25'
                : 'bg-sky-500 text-white hover:bg-sky-600 shadow-lg shadow-sky-500/25'
          }`}>
          {isLoading ? (
            <>
              <FiLoader className="w-4 h-4 animate-spin" />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <FiCode className="w-4 h-4" />
              <span>Generate Script</span>
            </>
          )}
        </button>

        <button
          onClick={exportScript}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${
            isLoading || sessionInfo.actionCount === 0
              ? isDarkMode
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : isDarkMode
                ? 'bg-[#2a2a5a] text-gray-200 hover:bg-[#3a3a6a] border border-[#2e2e60]'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
          }`}>
          {isLoading ? (
            <>
              <FiLoader className="w-4 h-4 animate-spin" />
              <span>Exporting...</span>
            </>
          ) : (
            <>
              <FiDownload className="w-4 h-4" />
              <span>Export Script</span>
            </>
          )}
        </button>

        <button
          onClick={reRunActions}
          disabled={isLoading || sessionInfo.actionCount === 0}
          className={`flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${
            isLoading || sessionInfo.actionCount === 0
              ? isDarkMode
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700 shadow-lg shadow-green-600/25'
          }`}>
          {isLoading ? (
            <>
              <FiLoader className="w-4 h-4 animate-spin" />
              <span>Re-running...</span>
            </>
          ) : (
            <>
              <FiPlay className="w-4 h-4" />
              <span>Re-run Actions</span>
            </>
          )}
        </button>

        <button
          onClick={resetTracker}
          disabled={isLoading}
          className={`flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${
            isLoading
              ? isDarkMode
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : isDarkMode
                ? 'bg-red-600/20 text-red-400 hover:bg-red-600/30 border border-red-600/30'
                : 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
          }`}>
          <FiTrash2 className="w-4 h-4" />
          <span>Reset Tracker</span>
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className={`rounded-lg border p-4 ${
          isDarkMode
            ? 'border-red-600/30 bg-red-900/20'
            : 'border-red-200 bg-red-50'
        }`}>
          <div className="flex items-center space-x-2">
            <FiAlertCircle className={`w-4 h-4 ${
              isDarkMode ? 'text-red-400' : 'text-red-600'
            }`} />
            <p className={`text-sm font-medium ${
              isDarkMode ? 'text-red-400' : 'text-red-600'
            }`}>
              {error}
            </p>
          </div>
        </div>
      )}

      {/* Generated Script Preview */}
      {generatedScript && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FiCheckCircle className={`w-4 h-4 ${
                isDarkMode ? 'text-green-400' : 'text-green-600'
              }`} />
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-gray-200' : 'text-gray-700'
              }`}>
                Generated Script Preview
              </h3>
            </div>
            <button
              onClick={() => {
                navigator.clipboard.writeText(generatedScript);
              }}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 ${
                isDarkMode
                  ? 'bg-[#875bf8] text-white hover:bg-[#a478f9] shadow-lg shadow-[#875bf8]/25'
                  : 'bg-sky-500 text-white hover:bg-sky-600 shadow-lg shadow-sky-500/25'
              }`}>
              <FiCopy className="w-4 h-4" />
              <span>Copy to Clipboard</span>
            </button>
          </div>

          <div className={`overflow-x-auto rounded-lg border ${
            isDarkMode
              ? 'bg-[#0d0d22] border-[#2e2e60]'
              : 'bg-gray-900 border-gray-700'
          }`}>
            <div className={`p-4 border-b ${
              isDarkMode ? 'border-[#2e2e60]' : 'border-gray-700'
            }`}>
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <span className="text-xs text-gray-400 font-mono">
                  {scriptOptions.language === 'typescript' ? 'test.ts' :
                   scriptOptions.language === 'python' ? 'test.py' :
                   scriptOptions.language === 'java' ? 'Test.java' :
                   scriptOptions.language === 'csharp' ? 'Test.cs' : 'test.js'}
                </span>
              </div>
            </div>
            <div className="p-4">
              <pre className={`whitespace-pre-wrap text-sm font-mono leading-relaxed ${
                isDarkMode ? 'text-gray-100' : 'text-gray-100'
              }`}>
                {generatedScript}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* No Actions Message */}
      {sessionInfo.actionCount === 0 && (
        <div className={`rounded-lg border p-4 ${
          isDarkMode
            ? 'border-yellow-600/30 bg-yellow-900/20'
            : 'border-yellow-200 bg-yellow-50'
        }`}>
          <div className="flex items-center space-x-2">
            <FiInfo className={`w-4 h-4 ${
              isDarkMode ? 'text-yellow-400' : 'text-yellow-600'
            }`} />
            <p className={`text-sm font-medium ${
              isDarkMode ? 'text-yellow-400' : 'text-yellow-700'
            }`}>
              No actions have been tracked yet. Start performing actions in the browser to generate a Playwright script.
            </p>
          </div>
        </div>
      )}

      {/* Info about executable actions */}
      {sessionInfo.actionCount > 0 && (
        <div className={`rounded-lg border p-4 ${
          isDarkMode
            ? 'border-blue-600/30 bg-blue-900/20'
            : 'border-blue-200 bg-blue-50'
        }`}>
          <div className="flex items-start space-x-2">
            <FiInfo className={`w-4 h-4 mt-0.5 ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div>
              <p className={`text-sm font-medium ${
                isDarkMode ? 'text-blue-400' : 'text-blue-700'
              }`}>
                <strong>Re-run Actions:</strong> Only browser interactions (clicks, navigation, input, etc.) will be
                executed. AI completion messages and other non-executable actions will be skipped.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
