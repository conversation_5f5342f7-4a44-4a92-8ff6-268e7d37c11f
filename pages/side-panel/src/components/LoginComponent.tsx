/* eslint-disable tailwindcss/no-custom-classname */
import React, { useState, useEffect } from 'react';
import { loginStore, type DrCodeLoginData } from '@extension/storage';
import ProjectSelector from './ProjectSelector';
import { FiUser, FiLogOut, FiLoader } from 'react-icons/fi';

interface LoginComponentProps {
  onLoginSuccess?: () => void;
  onLogout?: () => void;
  isDarkMode?: boolean;
  variant?: 'default' | 'settings'; // Add variant prop for different contexts
}

const LoginComponent: React.FC<LoginComponentProps> = ({ onLoginSuccess, onLogout, isDarkMode = false, variant = 'default' }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState<DrCodeLoginData['userData'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [loginTabId, setLoginTabId] = useState<number | null>(null);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    try {
      const loggedIn = await loginStore.isLoggedIn();
      setIsLoggedIn(loggedIn);

      if (loggedIn) {
        const user = await loginStore.getUserData();
        setUserData(user);
      }
    } catch (error) {
      console.error('Failed to check login status:', error);
    }
  };

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      // Open login page in new tab
      const tab = await chrome.tabs.create({
        url: 'https://www.drcode.ai/login',
        active: true,
      });

      if (tab.id) {
        setLoginTabId(tab.id);

        // Monitor the tab for login completion
        monitorLoginTab(tab.id);
      }
    } catch (error) {
      console.error('Failed to open login page:', error);
      setIsLoading(false);
    }
  };

  const monitorLoginTab = (tabId: number) => {
    const maxAttempts = 30; // 30 seconds
    let attempts = 0;

    const checkLogin = async () => {
      attempts++;

      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            const sessionId = localStorage.getItem('sessionId');
            const userDataStr = localStorage.getItem('userData');

            if (!sessionId || !userDataStr) {
              return null;
            }

            try {
              const userData = JSON.parse(userDataStr);
              return { sessionId, userData };
            } catch (error) {
              console.error('Failed to parse userData:', error);
              return null;
            }
          },
        });

        const result = results[0]?.result;

        if (result) {
          // Store login data
          await loginStore.setLoginData(result.sessionId, result.userData);

          // Update UI
          setIsLoggedIn(true);
          setUserData(result.userData);
          setIsLoading(false);
          setLoginTabId(null);

          // Close the login tab
          await chrome.tabs.remove(tabId);

          // Call success callback
          onLoginSuccess?.();

          return;
        }

        if (attempts >= maxAttempts) {
          console.log('Login monitoring timed out');
          setIsLoading(false);
          setLoginTabId(null);
          return;
        }

        // Check again in 1 second
        setTimeout(checkLogin, 1000);
      } catch (error) {
        console.error('Failed to check login status:', error);
        setIsLoading(false);
        setLoginTabId(null);
      }
    };

    // Start monitoring after a short delay
    setTimeout(checkLogin, 1000);
  };

  const handleLogout = async () => {
    try {
      // const headers = await loginStore.getHeaders();
      // console.log('headers', headers);
      await loginStore.clearLoginData();
      setIsLoggedIn(false);
      setUserData(null);
      onLogout?.();
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  };

  if (isLoggedIn && userData) {
    return (
      <div className={variant === 'settings' ? 'space-y-6' : `space-y-6 p-6 rounded-xl border transition-all duration-200 ${
        isDarkMode
          ? 'border-[#2e2e60]'
          : 'border-sky-200'
      }`}>
        {/* User Profile Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Avatar */}
            <div className={`relative w-12 h-12 rounded-full overflow-hidden border-2 transition-colors ${
              isDarkMode ? 'border-[#875bf8]' : 'border-sky-400'
            }`}>
              {userData.orgs?.[0]?.avatar_url ? (
                <img
                  src={userData.orgs[0].avatar_url}
                  alt={userData.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className={`w-full h-full flex items-center justify-center text-lg font-semibold ${
                  isDarkMode
                    ? 'bg-[#875bf8] text-white'
                    : 'bg-sky-400 text-white'
                }`}>
                  {userData.name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            {/* User Details */}
            <div className="flex flex-col space-y-1">
              <h3 className={`font-semibold text-lg ${
                isDarkMode ? 'text-gray-200' : 'text-gray-800'
              }`}>
                {userData.name}
              </h3>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                @{userData.username}
              </p>
            </div>
          </div>

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 ${
              isDarkMode
                ? 'bg-red-600/20 text-red-400 hover:bg-red-600/30 border border-red-600/30'
                : 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
            }`}
          >
            <FiLogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>

        {/* Project Section */}
        <div className={`border-t pt-4 ${
          isDarkMode ? 'border-[#2e2e60]' : 'border-sky-200'
        }`}>
          <ProjectSelector isDarkMode={isDarkMode} />
        </div>
      </div>
    );
  }

  return (
    <div className={variant === 'settings' ? 'space-y-6' : `space-y-6 p-8 rounded-xl border transition-all duration-200 ${
      isDarkMode
        ? 'border-[#2e2e60]'
        : 'border-sky-200'
    }`}>
      {/* Header Section */}
      <div className="text-center space-y-4">
        {/* Icon */}
        <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
          isDarkMode
            ? 'bg-[#875bf8]/20 border-2 border-[#875bf8]'
            : 'bg-sky-100 border-2 border-sky-400'
        }`}>
          <FiUser className={`w-8 h-8 ${
            isDarkMode ? 'text-[#875bf8]' : 'text-sky-600'
          }`} />
        </div>

        {/* Title and Description */}
        <div className="space-y-2">
          <h3 className={`text-2xl font-bold ${
            isDarkMode ? 'text-gray-200' : 'text-gray-800'
          }`}>
            DrCode Login
          </h3>
          <p className={`text-base ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Sign in to access DrCode features and start testing
          </p>
        </div>
      </div>

      {/* Login Button */}
      <div className="space-y-4">
        <button
          onClick={handleLogin}
          disabled={isLoading}
          className={`w-full flex items-center justify-center space-x-3 px-6 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:scale-105 disabled:hover:scale-100 ${
            isLoading
              ? isDarkMode
                ? 'bg-[#875bf8]/50 text-gray-300 cursor-not-allowed'
                : 'bg-sky-300 text-gray-500 cursor-not-allowed'
              : isDarkMode
                ? 'bg-[#875bf8] text-white hover:bg-[#a478f9] shadow-lg shadow-[#875bf8]/25'
                : 'bg-sky-500 text-white hover:bg-sky-600 shadow-lg shadow-sky-500/25'
          }`}
        >
          {isLoading ? (
            <>
              <FiLoader className="w-5 h-5 animate-spin" />
              <span>Opening Login Page...</span>
            </>
          ) : (
            <>
              <FiUser className="w-5 h-5" />
              <span>Login with DrCode</span>
            </>
          )}
        </button>

        {/* Loading Status */}
        {isLoading && (
          <div className={`p-4 rounded-lg border ${
            isDarkMode
              ? 'border-[#2e2e60] text-gray-300'
              : 'border-sky-200 text-gray-700'
          }`}>
            <div className="text-center space-y-2">
              <p className="font-medium">Please complete login in the opened tab</p>
              <p className="text-sm opacity-80">This window will update automatically</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginComponent;
