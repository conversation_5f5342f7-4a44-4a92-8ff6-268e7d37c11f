/* Add this at the top of the file */
body {
  border-color: rgb(159, 6, 11) !important; /* sky-200 color */
}

/* Remove unused classes: .App, .App-logo, .App-header */

code {
  background: rgba(186, 230, 253, 0.4);
  border-radius: 0.25rem;
  padding: 0.2rem 0.5rem;
  color: #0369a1;
}

/* Dark mode support for code */
@media (prefers-color-scheme: dark) {
  code {
    background: rgba(30, 58, 138, 0.4);
    color: #7dd3fc;
  }
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f0f9ff; /* sky-50 */
}

::-webkit-scrollbar-thumb {
  background: #875bf8; /* purple-500 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a478f9; /* purple-400 */
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: #0d0d22; /* dark background */
  }

  ::-webkit-scrollbar-thumb {
    background: #2e2e60; /* dark purple */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #875bf8; /* purple-500 */
  }
}

/* Used classes from the component */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: transparent;
  border-bottom: 1px solid rgba(46, 46, 96, 0.2);
}

/* Dark mode header border */
@media (prefers-color-scheme: dark) {
  .header {
    border-bottom: 1px solid rgba(46, 46, 96, 0.3);
  }
}

.header-logo {
  display: flex;
  align-items: center;
}

.header-icons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.header-icon {
  color: #875bf8;
  cursor: pointer;
  transition: color 0.2s ease;
  opacity: 0.8;
}

.header-icon:hover {
  color: #a478f9;
  opacity: 1;
}

.send-button {
  color: #875bf8;
  cursor: pointer;
  transition: color 0.2s ease;
  opacity: 0.8;
}

.send-button:hover {
  color: #a478f9;
  opacity: 1;
}

.send-button:disabled {
  color: #cbd5e1;
  cursor: not-allowed;
  opacity: 0.5;
}

/* Add these styles to your existing CSS */
.scrollbar-gutter-stable {
  scrollbar-gutter: stable;
}

/* Optional: Style the scrollbar for webkit browsers */
.scrollbar-gutter-stable::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-gutter-stable::-webkit-scrollbar-track {
  background: #f0f9ff; /* sky-50 */
}

.scrollbar-gutter-stable::-webkit-scrollbar-thumb {
  background: #875bf8; /* purple-500 */
  border-radius: 4px;
}

.scrollbar-gutter-stable::-webkit-scrollbar-thumb:hover {
  background: #a478f9; /* purple-400 */
}

/* Dark mode scrollbar for scrollbar-gutter-stable */
@media (prefers-color-scheme: dark) {
  .scrollbar-gutter-stable::-webkit-scrollbar-track {
    background: #0d0d22; /* dark background */
  }

  .scrollbar-gutter-stable::-webkit-scrollbar-thumb {
    background: #2e2e60; /* dark purple */
  }

  .scrollbar-gutter-stable::-webkit-scrollbar-thumb:hover {
    background: #875bf8; /* purple-500 */
  }
}

/* Dark mode text and background colors */
@media (prefers-color-scheme: dark) {
  .dark-mode-text {
    color: #e2e8f0 !important; /* slate-200 */
  }

  .dark-mode-bg {
    background-color: #1e293b !important; /* slate-800 */
  }

  .dark-mode-border {
    border-color: #475569 !important; /* slate-600 */
  }
}

/* Account Section Styles */
.account-section {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.account-section.dark-mode {
  background: #1e293b;
  border-color: #475569;
  color: #e2e8f0;
}

.user-profile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.account-section.dark-mode .user-profile {
  border-bottom-color: #475569;
}

.project-section {
  margin-top: 16px;
}

/* Project Selector Styles */
.project-selector {
  margin-top: 16px;
}

.project-selector h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.project-selector.dark-mode h3 {
  color: #f1f5f9;
}

.project-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.project-selector.dark-mode .project-item {
  background: #334155;
  border-color: #475569;
}

.project-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.project-selector.dark-mode .project-item:hover {
  background: #475569;
  border-color: #64748b;
}

.project-item.selected {
  background: #875bf8;
  border-color: #875bf8;
  color: white;
}

.project-selector.dark-mode .project-item.selected {
  background: #a478f9;
  border-color: #a478f9;
}

.project-info {
  flex: 1;
}

.project-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.project-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  opacity: 0.7;
}

.selected-indicator {
  font-weight: bold;
  color: white;
}

.selected-project-info {
  margin-top: 16px;
  padding: 12px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 14px;
}

.project-selector.dark-mode .selected-project-info {
  background: #334155;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #875bf8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  color: #ef4444;
  margin-bottom: 12px;
}

.project-selector.dark-mode .error-message {
  color: #f87171;
}

.retry-button {
  padding: 8px 16px;
  background: #875bf8;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #a478f9;
}

/* Login Component Styles */
.login-container {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.login-container.dark-mode {
  background: #1e293b;
  border-color: #475569;
  color: #e2e8f0;
}

.login-content {
  text-align: center;
}

.login-content h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.login-container.dark-mode .login-content h3 {
  color: #f1f5f9;
}

.login-content p {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #6b7280;
}

.login-container.dark-mode .login-content p {
  color: #94a3b8;
}

.login-button {
  width: 100%;
  padding: 14px 16px;
  background: #875bf8;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover:not(:disabled) {
  background: #a478f9;
}

.login-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.login-status {
  margin-top: 12px;
  padding: 12px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
}

.login-container.dark-mode .login-status {
  background: #334155;
  color: #94a3b8;
}

.login-status p {
  margin: 4px 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #875bf8;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-section.dark-mode .user-name {
  color: #f1f5f9;
}

.user-username {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.account-section.dark-mode .user-username {
  color: #94a3b8;
}

.logout-button {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.account-section.dark-mode .logout-button {
  background: #334155;
  color: #94a3b8;
  border-color: #475569;
}

.account-section.dark-mode .logout-button:hover {
  background: #475569;
  color: #e2e8f0;
}
